import {
    _decorator,
    Camera,
    Component,
    instantiate,
    Node,
    NodeEventType,
    RenderTexture,
    v4,
    Vec3
} from "cc";
const { ccclass, property } = _decorator;

@ccclass("Ctrl")
export class Ctrl extends Component {
    @property(Camera)
    mainCam: Camera;

    @property(Node)
    water: Node;

    @property(RenderTexture)
    rtReflect: RenderTexture;

    reflectCam: Camera;
    viewSpaceProbe = v4();

    onLoad() {
        this._initReflectCam();
        this._syncReflectCam();
    }

    private _initReflectCam() {
        const camNode = instantiate(this.mainCam.node);
        camNode.name = "reflectCam";
        camNode.parent = this.mainCam.node.parent;
        this.reflectCam = camNode.getComponent(Camera);
        this.reflectCam.targetTexture = this.rtReflect;
        const up = this.water.up.clone().negative();
        this.viewSpaceProbe.set(
            up.x,
            up.y,
            up.z,
            -Vec3.dot(up, this.water.worldPosition)
        );
        this.mainCam.node.on(NodeEventType.TRANSFORM_CHANGED, this._syncReflectCam, this);
    }

    private _syncReflectCam() {
        // 以water为镜面对称中心，将reflectCam的位置以及朝向设置为mainCam的对称
        const waterPos = this.water.worldPosition;
        const waterNormal = this.water.up;
        const mainCamPos = this.mainCam.node.worldPosition;

        // Calculate reflected position
        const toCamera = Vec3.subtract(new Vec3(), mainCamPos, waterPos);
        const distance = Vec3.dot(toCamera, waterNormal);
        const reflectedPos = Vec3.subtract(new Vec3(), mainCamPos, Vec3.multiplyScalar(new Vec3(), waterNormal, 2 * distance));

        // Set reflected camera position
        this.reflectCam.node.worldPosition = reflectedPos;

        // Calculate reflected rotation
        const mainCamForward = this.mainCam.node.forward;
        const reflectedForward = Vec3.subtract(new Vec3(), mainCamForward, Vec3.multiplyScalar(new Vec3(), waterNormal, 2 * Vec3.dot(mainCamForward, waterNormal)));
        this.reflectCam.node.lookAt(Vec3.add(new Vec3(), reflectedPos, reflectedForward), waterNormal);

        this.viewSpaceProbe.transformMat4(
            this.reflectCam.camera.matView.clone().invert().transpose()
        );
        this.reflectCam.camera.calculateObliqueMat(this.viewSpaceProbe);
    }
}
