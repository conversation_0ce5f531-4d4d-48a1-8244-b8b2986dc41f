import {
    assert, gfx, Material, rendering,
} from 'cc';

import {
    PipelineConfigs, CameraConfigs, PipelineContext, ToneMappingPassConfigs
} from './pipeline-configs';
import { addCopyToScreenPass } from './pipeline-utils';

const { LoadOp, StoreOp } = gfx;

/**
 * 内置色调映射Pass构建器
 */
export class BuiltinToneMappingPassBuilder implements rendering.PipelinePassBuilder {
    getConfigOrder(): number {
        return 0;
    }
    getRenderOrder(): number {
        return 300;
    }
    configCamera(
        camera: Readonly<renderer.scene.Camera>,
        pipelineConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & ToneMappingPassConfigs): void {
        cameraConfigs.enableToneMapping
            = cameraConfigs.enableHDR
            && !!cameraConfigs.settings.toneMapping.material;
        if (cameraConfigs.enableToneMapping) {
            ++cameraConfigs.remainingPasses;
        }
    }
    windowResize(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & ToneMappingPassConfigs,
        window: renderer.RenderWindow): void {
        // Tone mapping pass doesn't need additional render targets
    }

    setup(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & ToneMappingPassConfigs,
        camera: renderer.scene.Camera,
        context: PipelineContext,
        prevRenderPass?: rendering.BasicRenderPassBuilder)
        : rendering.BasicRenderPassBuilder | undefined {
        if (!cameraConfigs.enableToneMapping) {
            return prevRenderPass;
        }

        --cameraConfigs.remainingPasses;
        assert(cameraConfigs.remainingPasses >= 0);
        assert(!!cameraConfigs.settings.toneMapping.material);

        const id = camera.window.renderWindowId;
        const QueueHint = rendering.QueueHint;

        // Tone mapping pass
        const pass = ppl.addRenderPass(cameraConfigs.width, cameraConfigs.height, 'cc-tone-mapping');
        if (cameraConfigs.remainingPasses === 0) {
            pass.addRenderTarget(cameraConfigs.colorName, LoadOp.CLEAR, StoreOp.STORE);
        } else {
            pass.addRenderTarget(context.colorName, LoadOp.CLEAR, StoreOp.STORE);
        }
        pass.addTexture(context.colorName, 'inputTexture');
        pass.setVec4('g_platform', pplConfigs.platform);
        pass
            .addQueue(QueueHint.OPAQUE)
            .addFullscreenQuad(cameraConfigs.settings.toneMapping.material, 0);

        if (cameraConfigs.remainingPasses === 0) {
            return pass;
        } else {
            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, context.colorName);
        }
    }
}
