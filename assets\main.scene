[{"__type__": "cc.SceneAsset", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}, {"__id__": 8}, {"__id__": 11}, {"__id__": 24}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 34}, "_id": "20ba5412-4783-4159-9daf-165d312220cc"}, {"__type__": "cc.Node", "_name": "light", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": -0.06397656665577071, "y": -0.44608233363525845, "z": -0.8239028751062036, "w": -0.3436591377065261}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -117.894, "y": -194.909, "z": 38.562}, "_id": "c0y6F5f+pAvI805TdmxIjx"}, {"__type__": "cc.DirectionalLight", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 255, "g": 250, "b": 240, "a": 255}, "_useColorTemperature": false, "_colorTemperature": 6550, "_staticSettings": {"__id__": 4}, "_visibility": -325058561, "_illuminanceHDR": 65000, "_illuminance": 65000, "_illuminanceLDR": 1.6927083333333335, "_shadowEnabled": false, "_shadowPcf": 0, "_shadowBias": 1e-05, "_shadowNormalBias": 0, "_shadowSaturation": 1, "_shadowDistance": 50, "_shadowInvisibleOcclusionRange": 200, "_csmLevel": 4, "_csmLayerLambda": 0.75, "_csmOptimizationMode": 2, "_csmAdvancedOptions": false, "_csmLayersTransition": false, "_csmTransitionRange": 0.05, "_shadowFixedArea": false, "_shadowNear": 0.1, "_shadowFar": 10, "_shadowOrthoSize": 5, "_id": "597uMYCbhEtJQc0ffJlcgA"}, {"__type__": "cc.StaticLightSettings", "_baked": false, "_editorOnly": false, "_castShadow": false}, {"__type__": "cc.Node", "_name": "camera", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 6}, {"__id__": 7}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -2.9483089394606528, "y": 1.8935282482343065, "z": 6.827786335441143}, "_lrot": {"__type__": "cc.Quat", "x": -0.11241323196958086, "y": -0.20345811488547227, "z": -0.0235224090482484, "w": 0.9723244093578707}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -13.189703622811416, "y": -23.637145884506296, "z": -3.2337506631460816e-06}, "_id": "c9DMICJLFO5IeO07EPon7U"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_projection": 1, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 10, "_near": 1, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 51, "g": 51, "b": 51, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 14, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1822425087, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "7dWQTpwS5LrIHnc1zAPUtf"}, {"__type__": "23bc4CxSmZOdbTTLkFxPVj/", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_settings": {"msaa": {"enabled": false, "sampleCount": 4}, "enableShadingScale": false, "shadingScale": 0.5, "bloom": {"enabled": false, "material": null, "enableAlphaMask": false, "iterations": 3, "threshold": 0.8, "intensity": 2.3}, "toneMapping": {"material": null}, "colorGrading": {"enabled": false, "material": null, "contribute": 1, "colorGradingMap": null}, "fsr": {"enabled": false, "material": null, "sharpness": 0.8}, "fxaa": {"enabled": false, "material": null}}, "_editorPreview": false, "_id": "b7ktVqh/NH8aidGS8V3Iiv"}, {"__type__": "cc.Node", "_name": "ctrl", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 9}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9bn7T8eBhGPYkYwnH8r/5Z"}, {"__type__": "ab21etIJ3FG4YMV8VfPXARw", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "mainCam": {"__id__": 6}, "water": {"__id__": 10}, "rtReflect": {"__uuid__": "9100bbd3-0676-4551-9ab6-c560237e7aea", "__expectedType__": "cc.RenderTexture"}, "_id": "bd0/3ci4BBOY6VMtMTeSz4"}, {"__type__": "cc.Node", "_name": "water", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 22}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "20U3HkohRL4rzgtOA3Qy/g"}, {"__type__": "cc.Node", "_name": "scene", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 10}, {"__id__": 12}, {"__id__": 15}, {"__id__": 18}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5eWNGe4x9KOaKNA/FUUbLv"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 13}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.25881904510252074, "w": 0.9659258262890683}, "_lscale": {"__type__": "cc.Vec3", "x": 3, "y": 3, "z": 3}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 30}, "_id": "48bkHuax5MqIddgvQBNCX4"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Torus<ModelComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "620b6bf3-0369-4560-837f-2a2c00b73c26", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 14}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@40ece", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_reflectionProbeBlendId": -1, "_reflectionProbeBlendWeight": 0, "_enabledGlobalStandardSkinObject": false, "_enableMorph": true, "_id": "5bH+hju39EsLaugqWdxwyI"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true}, {"__type__": "cc.Node", "_name": "Capsule", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 16}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -1.973, "y": 0.856, "z": 0.933}, "_lrot": {"__type__": "cc.Quat", "x": 0.40240319473321123, "y": 0, "z": 0, "w": 0.9154625436731452}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 47.457, "y": 0, "z": 0}, "_id": "5drVh2NLBOw6o74VhfQDkA"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Capsule<ModelComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "620b6bf3-0369-4560-837f-2a2c00b73c26", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 17}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@801ec", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_reflectionProbeBlendId": -1, "_reflectionProbeBlendWeight": 0, "_enabledGlobalStandardSkinObject": false, "_enableMorph": true, "_id": "ffZ1XeViJCqLIdigcIM5O3"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true}, {"__type__": "cc.Node", "_name": "test", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 19}], "_active": false, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0.5903662116821635, "y": 0, "z": 0, "w": 0.8071355128502592}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 72.366, "y": 0, "z": 0}, "_id": "0d/M1WdwBGaoGmEZFOoYUe"}, {"__type__": "cc.Node", "_name": "Plane", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 18}, "_children": [], "_active": true, "_components": [{"__id__": 20}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0.328, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.2, "y": 0.2, "z": 0.2}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "49PyRMOspN0qSNOQ/Op9Y/"}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Plane<ModelComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "620b6bf3-0369-4560-837f-2a2c00b73c26", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 21}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_reflectionProbeBlendId": -1, "_reflectionProbeBlendWeight": 0, "_enabledGlobalStandardSkinObject": false, "_enableMorph": true, "_id": "4fmRFrDfBGopbYE92sgUUD"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "Plane<ModelComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_materials": [{"__uuid__": "10440dbf-587d-447d-81f5-9407e6bdc1d1", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 23}, "_mesh": {"__uuid__": "1263d74c-8167-4928-91a6-4e2672411f47@2e76e", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 0, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": 0, "_reflectionProbeBlendId": -1, "_reflectionProbeBlendWeight": 0, "_enabledGlobalStandardSkinObject": false, "_enableMorph": true, "_id": "6bQPCNzVlKSpT2nJ8vBmIg"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": false, "_receiveShadow": false, "_recieveShadow": false, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": false}, {"__type__": "cc.Node", "_name": "canvas", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 25}, {"__id__": 27}], "_active": true, "_components": [{"__id__": 31}, {"__id__": 32}, {"__id__": 33}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 640, "y": 360, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "59jE+XNthOk53839XK1FpS"}, {"__type__": "cc.Node", "_name": "ui camera", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [{"__id__": 26}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "01/Pu7MhBHNa9BKK1zomrx"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 1073741824, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 470.97065462753955, "_near": 1, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 41943040, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "cd5ySYzt9OWrqExB+gfy1M"}, {"__type__": "cc.Node", "_name": "rt", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [{"__id__": 28}, {"__id__": 29}, {"__id__": 30}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 512, "y": -232, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5fxhBULHNG5KUfvwS2Fvdb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 256, "height": 256}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e7ZvhrHUlFppj9kpoMv+CW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9100bbd3-0676-4551-9ab6-c560237e7aea@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "8bS95C41xIYKDyWf1AFgVi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": null, "_alignFlags": 36, "_target": null, "_left": 512, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 36, "_id": "2eYQsjuf9PypXuDn9BwDZB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "78obRUwoRKGqU9A7bDru/v"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 26}, "_alignCanvasWithScreen": true, "_id": "92siMh3hlKG6cUTbktkIbs"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "78J1bCIHRAwZz7Fgx5oZ69"}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 35}, "shadows": {"__id__": 36}, "_skybox": {"__id__": 37}, "fog": {"__id__": 38}, "octree": {"__id__": 39}, "skin": {"__id__": 40}, "lightProbeInfo": {"__id__": 41}, "postSettings": {"__id__": 42}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.452588, "y": 0.607642, "z": 0.755699, "w": 0}, "_skyIllumLDR": 0.8, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.618555, "y": 0.577848, "z": 0.544564, "w": 0}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 1024, "y": 1024}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": {"__uuid__": "d032ac98-05e1-4090-88bb-eb640dcb5fc1@b47c0", "__expectedType__": "cc.TextureCube"}, "_envmap": {"__uuid__": "d032ac98-05e1-4090-88bb-eb640dcb5fc1@b47c0", "__expectedType__": "cc.TextureCube"}, "_envmapLDR": {"__uuid__": "6f01cf7f-81bf-4a7e-bd5d-0afc19696480@b47c0", "__expectedType__": "cc.TextureCube"}, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": true, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": true, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]