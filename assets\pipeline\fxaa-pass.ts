import {
    assert, gfx, Material, rendering,
} from 'cc';

import {
    PipelineConfigs, CameraConfigs, PipelineContext, FXAAPassConfigs
} from './pipeline-configs';
import { addCopyToScreenPass } from './pipeline-utils';

const { LoadOp, StoreOp } = gfx;

/**
 * 内置FXAA抗锯齿Pass构建器
 */
export class BuiltinFXAAPassBuilder implements rendering.PipelinePassBuilder {
    getConfigOrder(): number {
        return 0;
    }
    getRenderOrder(): number {
        return 400;
    }
    configCamera(
        camera: Readonly<renderer.scene.Camera>,
        pipelineConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & FXAAPassConfigs): void {
        cameraConfigs.enableFXAA
            = cameraConfigs.settings.fxaa.enabled
            && !!cameraConfigs.settings.fxaa.material;
        if (cameraConfigs.enableFXAA) {
            ++cameraConfigs.remainingPasses;
        }
    }
    windowResize(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & FXAAPassConfigs,
        window: renderer.RenderWindow): void {
        // FXAA pass doesn't need additional render targets
    }

    setup(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & FXAAPassConfigs,
        camera: renderer.scene.Camera,
        context: PipelineContext,
        prevRenderPass?: rendering.BasicRenderPassBuilder)
        : rendering.BasicRenderPassBuilder | undefined {
        if (!cameraConfigs.enableFXAA) {
            return prevRenderPass;
        }

        --cameraConfigs.remainingPasses;
        assert(cameraConfigs.remainingPasses >= 0);
        assert(!!cameraConfigs.settings.fxaa.material);

        const id = camera.window.renderWindowId;
        const QueueHint = rendering.QueueHint;

        // FXAA pass
        const pass = ppl.addRenderPass(cameraConfigs.width, cameraConfigs.height, 'cc-fxaa');
        if (cameraConfigs.remainingPasses === 0) {
            pass.addRenderTarget(cameraConfigs.colorName, LoadOp.CLEAR, StoreOp.STORE);
        } else {
            pass.addRenderTarget(context.colorName, LoadOp.CLEAR, StoreOp.STORE);
        }
        pass.addTexture(context.colorName, 'sceneColorMap');
        pass.setVec4('g_platform', pplConfigs.platform);
        pass
            .addQueue(QueueHint.OPAQUE)
            .addFullscreenQuad(cameraConfigs.settings.fxaa.material, 0);

        if (cameraConfigs.remainingPasses === 0) {
            return pass;
        } else {
            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, context.colorName);
        }
    }
}
