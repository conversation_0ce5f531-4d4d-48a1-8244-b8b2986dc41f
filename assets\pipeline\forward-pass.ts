import {
    assert, cclegacy, geometry, gfx,
    ReflectionProbeManager, renderer,
    rendering, Vec3, warn
} from 'cc';

import { DEBUG, EDITOR } from 'cc/env';

import {
    CameraConfigs,
    ForwardPassConfigs,
    PipelineConfigs,
    PipelineContext
} from './pipeline-configs';
import {
    addCopyToScreenPass,
    forwardNeedClearColor, getCsmMainLightViewport
} from './pipeline-utils';

const { AABB, Sphere, intersect } = geometry;
const { ClearFlagBit, Color, Format, LoadOp, StoreOp, TextureType, Viewport } = gfx;
const { scene } = renderer;
const { CameraUsage, CSMLevel, LightType } = scene;

/**
 * 前向光照类
 */
class ForwardLighting {
    // Active lights
    private readonly lights: renderer.scene.Light[] = [];
    // Active spot lights with shadows (Mutually exclusive with `lights`)
    private readonly shadowEnabledSpotLights: renderer.scene.SpotLight[] = [];

    // Internal cached resources
    private readonly _sphere = Sphere.create(0, 0, 0, 1);
    private readonly _boundingBox = new AABB();
    private readonly _rangedDirLightBoundingBox = new AABB(0.0, 0.0, 0.0, 0.5, 0.5, 0.5);

    // ----------------------------------------------------------------
    // Interface
    // ----------------------------------------------------------------
    public cullLights(scene: renderer.RenderScene, frustum: geometry.Frustum, cameraPos?: Vec3): void {
        // TODO(zhouzhenglong): Make light culling native
        this.lights.length = 0;
        this.shadowEnabledSpotLights.length = 0;
        // spot lights
        for (const light of scene.spotLights) {
            if (light.baked) {
                continue;
            }
            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);
            if (intersect.sphereFrustum(this._sphere, frustum)) {
                if (light.shadowEnabled) {
                    this.shadowEnabledSpotLights.push(light);
                } else {
                    this.lights.push(light);
                }
            }
        }
        // sphere lights
        for (const light of scene.sphereLights) {
            if (light.baked) {
                continue;
            }
            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);
            if (intersect.sphereFrustum(this._sphere, frustum)) {
                this.lights.push(light);
            }
        }
        // point lights
        for (const light of scene.pointLights) {
            if (light.baked) {
                continue;
            }
            Sphere.set(this._sphere, light.position.x, light.position.y, light.position.z, light.range);
            if (intersect.sphereFrustum(this._sphere, frustum)) {
                this.lights.push(light);
            }
        }
        // ranged dir lights
        for (const light of scene.rangedDirLights) {
            AABB.transform(this._boundingBox, this._rangedDirLightBoundingBox, light.node!.getWorldMatrix());
            if (intersect.aabbFrustum(this._boundingBox, frustum)) {
                this.lights.push(light);
            }
        }

        if (cameraPos) {
            this.shadowEnabledSpotLights.sort(
                (lhs, rhs) => Vec3.squaredDistance(cameraPos, lhs.position) - Vec3.squaredDistance(cameraPos, rhs.position),
            );
        }
    }
    private _addLightQueues(camera: renderer.scene.Camera, pass: rendering.BasicRenderPassBuilder): void {
        for (const light of this.lights) {
            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');
            switch (light.type) {
                case LightType.SPHERE:
                    queue.name = 'sphere-light';
                    break;
                case LightType.SPOT:
                    queue.name = 'spot-light';
                    break;
                case LightType.POINT:
                    queue.name = 'point-light';
                    break;
                case LightType.RANGED_DIRECTIONAL:
                    queue.name = 'ranged-directional-light';
                    break;
                default:
                    queue.name = 'unknown-light';
            }
            queue.addScene(
                camera,
                rendering.SceneFlags.BLEND,
                light,
            );
        }
    }
    public addSpotlightShadowPasses(
        ppl: rendering.BasicPipeline,
        camera: renderer.scene.Camera,
        maxNumShadowMaps: number,
    ): void {
        let i = 0;
        for (const light of this.shadowEnabledSpotLights) {
            const shadowMapSize = ppl.pipelineSceneData.shadows.size;
            const shadowPass = ppl.addRenderPass(shadowMapSize.x, shadowMapSize.y, 'default');
            shadowPass.name = `SpotLightShadowPass${i}`;
            shadowPass.addRenderTarget(`SpotShadowMap${i}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));
            shadowPass.addDepthStencil(`SpotShadowDepth${i}`, LoadOp.CLEAR, StoreOp.DISCARD);
            shadowPass.addQueue(rendering.QueueHint.NONE, 'shadow-caster')
                .addScene(camera, rendering.SceneFlags.OPAQUE | rendering.SceneFlags.MASK | rendering.SceneFlags.SHADOW_CASTER)
                .useLightFrustum(light);
            ++i;
            if (i >= maxNumShadowMaps) {
                break;
            }
        }
    }
    public addLightQueues(pass: rendering.BasicRenderPassBuilder,
        camera: renderer.scene.Camera, maxNumShadowMaps: number): void {
        this._addLightQueues(camera, pass);
        let i = 0;
        for (const light of this.shadowEnabledSpotLights) {
            // Add spot-light pass
            // Save last RenderPass to the `pass` variable
            // TODO(zhouzhenglong): Fix per queue addTexture
            pass.addTexture(`SpotShadowMap${i}`, 'cc_spotShadowMap');
            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');
            queue.addScene(camera, rendering.SceneFlags.BLEND, light);
            ++i;
            if (i >= maxNumShadowMaps) {
                break;
            }
        }
    }

    // Notice: ForwardLighting cannot handle a lot of lights.
    // If there are too many lights, the performance will be very poor.
    // If many lights are needed, please implement a forward+ or deferred rendering pipeline.
    public addLightPasses(
        colorName: string,
        depthStencilName: string,
        depthStencilStoreOp: gfx.StoreOp,
        id: number, // window id
        width: number,
        height: number,
        camera: renderer.scene.Camera,
        viewport: gfx.Viewport,
        ppl: rendering.BasicPipeline,
        pass: rendering.BasicRenderPassBuilder,
    ): rendering.BasicRenderPassBuilder {
        this._addLightQueues(camera, pass);

        let count = 0;
        const shadowMapSize = ppl.pipelineSceneData.shadows.size;
        for (const light of this.shadowEnabledSpotLights) {
            const shadowPass = ppl.addRenderPass(shadowMapSize.x, shadowMapSize.y, 'default');
            shadowPass.name = 'SpotlightShadowPass';
            // Reuse csm shadow map
            shadowPass.addRenderTarget(`ShadowMap${id}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));
            shadowPass.addDepthStencil(`ShadowDepth${id}`, LoadOp.CLEAR, StoreOp.DISCARD);
            shadowPass.addQueue(rendering.QueueHint.NONE, 'shadow-caster')
                .addScene(camera, rendering.SceneFlags.OPAQUE | rendering.SceneFlags.MASK | rendering.SceneFlags.SHADOW_CASTER)
                .useLightFrustum(light);

            // Add spot-light pass
            // Save last RenderPass to the `pass` variable
            ++count;
            const storeOp = count === this.shadowEnabledSpotLights.length
                ? depthStencilStoreOp
                : StoreOp.STORE;

            pass = ppl.addRenderPass(width, height, 'default');
            pass.name = 'SpotlightWithShadowMap';
            pass.setViewport(viewport);
            pass.addRenderTarget(colorName, LoadOp.LOAD);
            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, storeOp);
            pass.addTexture(`ShadowMap${id}`, 'cc_spotShadowMap');
            const queue = pass.addQueue(rendering.QueueHint.BLEND, 'forward-add');
            queue.addScene(
                camera,
                rendering.SceneFlags.BLEND,
                light,
            );
        }
        return pass;
    }

    public isMultipleLightPassesNeeded(): boolean {
        return this.shadowEnabledSpotLights.length > 0;
    }
}

/**
 * 内置前向渲染Pass构建器
 */
export class BuiltinForwardPassBuilder implements rendering.PipelinePassBuilder {
    static ConfigOrder = 100;
    static RenderOrder = 100;
    getConfigOrder(): number {
        return BuiltinForwardPassBuilder.ConfigOrder;
    }
    getRenderOrder(): number {
        return BuiltinForwardPassBuilder.RenderOrder;
    }
    configCamera(
        camera: Readonly<renderer.scene.Camera>,
        pipelineConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & ForwardPassConfigs): void {
        // Shadow
        cameraConfigs.enableMainLightShadowMap = pipelineConfigs.shadowEnabled
            && !pipelineConfigs.usePlanarShadow
            && !!camera.scene
            && !!camera.scene.mainLight
            && camera.scene.mainLight.shadowEnabled;

        cameraConfigs.enableMainLightPlanarShadowMap = pipelineConfigs.shadowEnabled
            && pipelineConfigs.usePlanarShadow
            && !!camera.scene
            && !!camera.scene.mainLight
            && camera.scene.mainLight.shadowEnabled;

        // Reflection Probe
        cameraConfigs.enablePlanarReflectionProbe = cameraConfigs.isMainGameWindow
            || camera.cameraUsage === CameraUsage.SCENE_VIEW
            || camera.cameraUsage === CameraUsage.GAME_VIEW;

        // MSAA
        cameraConfigs.enableMSAA = cameraConfigs.settings.msaa.enabled
            && !cameraConfigs.enableStoreSceneDepth // Cannot store MS depth, resolve depth is also not cross-platform
            && !pipelineConfigs.isWeb // TODO(zhouzhenglong): remove this constraint
            && !pipelineConfigs.isWebGL1;

        // Forward rendering (Depend on MSAA and TBR)
        cameraConfigs.enableSingleForwardPass
            = pipelineConfigs.isMobile || cameraConfigs.enableMSAA;

        ++cameraConfigs.remainingPasses;
    }
    windowResize(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,
        window: renderer.RenderWindow,
        camera: renderer.scene.Camera,
        nativeWidth: number,
        nativeHeight: number): void {
        const ResourceFlags = rendering.ResourceFlags;
        const ResourceResidency = rendering.ResourceResidency;
        const id = window.renderWindowId;
        const settings = cameraConfigs.settings;

        const width = cameraConfigs.enableShadingScale
            ? Math.max(Math.floor(nativeWidth * cameraConfigs.shadingScale), 1)
            : nativeWidth;
        const height = cameraConfigs.enableShadingScale
            ? Math.max(Math.floor(nativeHeight * cameraConfigs.shadingScale), 1)
            : nativeHeight;

        // MsaaRadiance
        if (cameraConfigs.enableMSAA) {
            // Notice: We never store multisample results.
            // These samples are always resolved and discarded at the end of the render pass.
            // So the ResourceResidency should be MEMORYLESS.
            if (cameraConfigs.enableHDR) {
                ppl.addTexture(`MsaaRadiance${id}`, TextureType.TEX2D, cameraConfigs.radianceFormat, width, height, 1, 1, 1,
                    settings.msaa.sampleCount, ResourceFlags.COLOR_ATTACHMENT, ResourceResidency.MEMORYLESS);
            } else {
                ppl.addTexture(`MsaaRadiance${id}`, TextureType.TEX2D, Format.RGBA8, width, height, 1, 1, 1,
                    settings.msaa.sampleCount, ResourceFlags.COLOR_ATTACHMENT, ResourceResidency.MEMORYLESS);
            }
            ppl.addTexture(`MsaaDepthStencil${id}`, TextureType.TEX2D, Format.DEPTH_STENCIL, width, height, 1, 1, 1,
                settings.msaa.sampleCount, ResourceFlags.DEPTH_STENCIL_ATTACHMENT, ResourceResidency.MEMORYLESS);
        }

        // Mainlight ShadowMap
        ppl.addRenderTarget(
            `ShadowMap${id}`,
            pplConfigs.shadowMapFormat,
            pplConfigs.shadowMapSize.x,
            pplConfigs.shadowMapSize.y,
        );
        ppl.addDepthStencil(
            `ShadowDepth${id}`,
            Format.DEPTH_STENCIL,
            pplConfigs.shadowMapSize.x,
            pplConfigs.shadowMapSize.y,
        );

        // Spot-light shadow maps
        if (cameraConfigs.enableSingleForwardPass) {
            const count = pplConfigs.mobileMaxSpotLightShadowMaps;
            for (let i = 0; i !== count; ++i) {
                ppl.addRenderTarget(
                    `SpotShadowMap${i}`,
                    pplConfigs.shadowMapFormat,
                    pplConfigs.shadowMapSize.x,
                    pplConfigs.shadowMapSize.y,
                );
                ppl.addDepthStencil(
                    `SpotShadowDepth${i}`,
                    Format.DEPTH_STENCIL,
                    pplConfigs.shadowMapSize.x,
                    pplConfigs.shadowMapSize.y,
                );
            }
        }
    }
    setup(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & ForwardPassConfigs,
        camera: renderer.scene.Camera,
        context: PipelineContext): rendering.BasicRenderPassBuilder | undefined {
        const id = camera.window.renderWindowId;

        const scene = camera.scene!;
        const mainLight = scene.mainLight;

        --cameraConfigs.remainingPasses;
        assert(cameraConfigs.remainingPasses >= 0);

        // Forward Lighting (Light Culling)
        this.forwardLighting.cullLights(scene, camera.frustum);

        // Main Directional light CSM Shadow Map
        if (cameraConfigs.enableMainLightShadowMap) {
            assert(!!mainLight);
            this._addCascadedShadowMapPass(ppl, pplConfigs, id, mainLight, camera);
        }

        // Spot light shadow maps (Mobile or MSAA)
        if (cameraConfigs.enableSingleForwardPass) {
            // Currently, only support 1 spot light with shadow map on mobile platform.
            // TODO(zhouzhenglong): Relex this limitation.
            this.forwardLighting.addSpotlightShadowPasses(
                ppl, camera, pplConfigs.mobileMaxSpotLightShadowMaps);
        }

        this._tryAddReflectionProbePasses(ppl, cameraConfigs, id, mainLight, camera.scene);

        if (cameraConfigs.remainingPasses > 0 || cameraConfigs.enableShadingScale) {
            context.colorName = cameraConfigs.enableShadingScale
                ? `ScaledRadiance0_${id}`
                : `Radiance0_${id}`;
            context.depthStencilName = cameraConfigs.enableShadingScale
                ? `ScaledSceneDepth_${id}`
                : `SceneDepth_${id}`;
        } else {
            context.colorName = cameraConfigs.colorName;
            context.depthStencilName = cameraConfigs.depthStencilName;
        }

        const pass = this._addForwardRadiancePasses(
            ppl, pplConfigs, cameraConfigs, id, camera,
            cameraConfigs.width, cameraConfigs.height, mainLight,
            context.colorName, context.depthStencilName,
            !cameraConfigs.enableMSAA,
            cameraConfigs.enableStoreSceneDepth ? StoreOp.STORE : StoreOp.DISCARD);

        if (!cameraConfigs.enableStoreSceneDepth) {
            context.depthStencilName = '';
        }

        if (cameraConfigs.remainingPasses === 0 && cameraConfigs.enableShadingScale) {
            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, context.colorName);
        } else {
            return pass;
        }
    }

    private readonly forwardLighting = new ForwardLighting();
    private readonly _viewport = new Viewport();
    private readonly _clearColor = new Color(0, 0, 0, 1);
    private readonly _reflectionProbeClearColor = new Vec3(0, 0, 0);

    private _addCascadedShadowMapPass(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        id: number,
        light: renderer.scene.DirectionalLight,
        camera: renderer.scene.Camera,
    ): void {
        const QueueHint = rendering.QueueHint;
        const SceneFlags = rendering.SceneFlags;
        // ----------------------------------------------------------------
        // Dynamic states
        // ----------------------------------------------------------------
        const shadowSize = ppl.pipelineSceneData.shadows.size;
        const width = shadowSize.x;
        const height = shadowSize.y;

        const viewport = this._viewport;
        viewport.left = viewport.top = 0;
        viewport.width = width;
        viewport.height = height;

        // ----------------------------------------------------------------
        // CSM Shadow Map
        // ----------------------------------------------------------------
        const pass = ppl.addRenderPass(width, height, 'default');
        pass.name = 'CascadedShadowMap';
        pass.addRenderTarget(`ShadowMap${id}`, LoadOp.CLEAR, StoreOp.STORE, new Color(1, 1, 1, 1));
        pass.addDepthStencil(`ShadowDepth${id}`, LoadOp.CLEAR, StoreOp.DISCARD);
        const csmLevel = ppl.pipelineSceneData.csmSupported ? light.csmLevel : 1;

        // Add shadow map viewports
        for (let level = 0; level !== csmLevel; ++level) {
            getCsmMainLightViewport(light, width, height, level, this._viewport, pplConfigs.screenSpaceSignY);
            const queue = pass.addQueue(QueueHint.NONE, 'shadow-caster');
            if (!pplConfigs.isWebGPU) { // Temporary workaround for WebGPU
                queue.setViewport(this._viewport);
            }
            queue
                .addScene(camera, SceneFlags.OPAQUE | SceneFlags.MASK | SceneFlags.SHADOW_CASTER)
                .useLightFrustum(light, level);
        }
    }
    private _tryAddReflectionProbePasses(
        ppl: rendering.BasicPipeline,
        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,
        id: number,
        mainLight: renderer.scene.DirectionalLight | null,
        scene: renderer.RenderScene | null,
    ): void {
        const reflectionProbeManager = cclegacy.internal.reflectionProbeManager as ReflectionProbeManager | undefined;
        if (!reflectionProbeManager) {
            return;
        }
        const ResourceResidency = rendering.ResourceResidency;
        const probes = reflectionProbeManager.getProbes();
        const maxProbeCount = 4;
        let probeID = 0;
        for (const probe of probes) {
            if (!probe.needRender) {
                continue;
            }
            const area = probe.renderArea();
            const width = Math.max(Math.floor(area.x), 1);
            const height = Math.max(Math.floor(area.y), 1);

            if (probe.probeType === renderer.scene.ProbeType.PLANAR) {
                if (!cameraConfigs.enablePlanarReflectionProbe) {
                    continue;
                }
                const window: renderer.RenderWindow = probe.realtimePlanarTexture!.window!;
                const colorName = `PlanarProbeRT${probeID}`;
                const depthStencilName = `PlanarProbeDS${probeID}`;
                // ProbeResource
                ppl.addRenderWindow(colorName,
                    cameraConfigs.radianceFormat, width, height, window);
                ppl.addDepthStencil(depthStencilName,
                    gfx.Format.DEPTH_STENCIL, width, height, ResourceResidency.MEMORYLESS);

                // Rendering
                const probePass = ppl.addRenderPass(width, height, 'default');
                probePass.name = `PlanarReflectionProbe${probeID}`;
                this._buildReflectionProbePass(probePass, cameraConfigs, id, probe.camera,
                    colorName, depthStencilName, mainLight, scene);
            } else if (EDITOR) {
                for (let faceIdx = 0; faceIdx < probe.bakedCubeTextures.length; faceIdx++) {
                    probe.updateCameraDir(faceIdx);
                    const window: renderer.RenderWindow = probe.bakedCubeTextures[faceIdx].window!;
                    const colorName = `CubeProbeRT${probeID}${faceIdx}`;
                    const depthStencilName = `CubeProbeDS${probeID}${faceIdx}`;
                    // ProbeResource
                    ppl.addRenderWindow(colorName,
                        cameraConfigs.radianceFormat, width, height, window);
                    ppl.addDepthStencil(depthStencilName,
                        gfx.Format.DEPTH_STENCIL, width, height, ResourceResidency.MEMORYLESS);

                    // Rendering
                    const probePass = ppl.addRenderPass(width, height, 'default');
                    probePass.name = `CubeProbe${probeID}${faceIdx}`;
                    this._buildReflectionProbePass(probePass, cameraConfigs, id, probe.camera,
                        colorName, depthStencilName, mainLight, scene);
                }
                probe.needRender = false;
            }
            ++probeID;
            if (probeID === maxProbeCount) {
                break;
            }
        }
    }
    private _buildReflectionProbePass(
        pass: rendering.BasicRenderPassBuilder,
        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,
        id: number,
        camera: renderer.scene.Camera,
        colorName: string,
        depthStencilName: string,
        mainLight: renderer.scene.DirectionalLight | null,
        scene: renderer.RenderScene | null = null,
    ): void {
        const QueueHint = rendering.QueueHint;
        const SceneFlags = rendering.SceneFlags;
        // set viewport
        const colorStoreOp = cameraConfigs.enableMSAA ? StoreOp.DISCARD : StoreOp.STORE;

        // bind output render target
        if (forwardNeedClearColor(camera)) {
            this._reflectionProbeClearColor.x = camera.clearColor.x;
            this._reflectionProbeClearColor.y = camera.clearColor.y;
            this._reflectionProbeClearColor.z = camera.clearColor.z;
            const clearColor = rendering.packRGBE(this._reflectionProbeClearColor);
            this._clearColor.x = clearColor.x;
            this._clearColor.y = clearColor.y;
            this._clearColor.z = clearColor.z;
            this._clearColor.w = clearColor.w;
            pass.addRenderTarget(colorName, LoadOp.CLEAR, colorStoreOp, this._clearColor);
        } else {
            pass.addRenderTarget(colorName, LoadOp.LOAD, colorStoreOp);
        }

        // bind depth stencil buffer
        if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {
            pass.addDepthStencil(
                depthStencilName,
                LoadOp.CLEAR,
                StoreOp.DISCARD,
                camera.clearDepth,
                camera.clearStencil,
                camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,
            );
        } else {
            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, StoreOp.DISCARD);
        }

        // Set shadow map if enabled
        if (cameraConfigs.enableMainLightShadowMap) {
            pass.addTexture(`ShadowMap${id}`, 'cc_shadowMap');
        }

        // TODO(zhouzhenglong): Separate OPAQUE and MASK queue

        // add opaque and mask queue
        pass.addQueue(QueueHint.NONE, 'reflect-map') // Currently we put OPAQUE and MASK into one queue, so QueueHint is NONE
            .addScene(camera,
                SceneFlags.OPAQUE | SceneFlags.MASK | SceneFlags.REFLECTION_PROBE,
                mainLight || undefined,
                scene ? scene : undefined);
    }
    private _addForwardRadiancePasses(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,
        id: number,
        camera: renderer.scene.Camera,
        width: number,
        height: number,
        mainLight: renderer.scene.DirectionalLight | null,
        colorName: string,
        depthStencilName: string,
        disableMSAA: boolean = false,
        depthStencilStoreOp: gfx.StoreOp = StoreOp.DISCARD,
    ): rendering.BasicRenderPassBuilder {
        const QueueHint = rendering.QueueHint;
        const SceneFlags = rendering.SceneFlags;
        // ----------------------------------------------------------------
        // Dynamic states
        // ----------------------------------------------------------------
        // Prepare camera clear color
        const clearColor = camera.clearColor; // Reduce C++/TS interop
        this._clearColor.x = clearColor.x;
        this._clearColor.y = clearColor.y;
        this._clearColor.z = clearColor.z;
        this._clearColor.w = clearColor.w;

        // Prepare camera viewport
        const viewport = camera.viewport; // Reduce C++/TS interop
        this._viewport.left = Math.round(viewport.x * width);
        this._viewport.top = Math.round(viewport.y * height);
        // Here we must use camera.viewport.width instead of camera.viewport.z, which
        // is undefined on native platform. The same as camera.viewport.height.
        this._viewport.width = Math.max(Math.round(viewport.width * width), 1);
        this._viewport.height = Math.max(Math.round(viewport.height * height), 1);

        // MSAA
        const enableMSAA = !disableMSAA && cameraConfigs.enableMSAA;
        assert(!enableMSAA || cameraConfigs.enableSingleForwardPass);

        // ----------------------------------------------------------------
        // Forward Lighting (Main Directional Light)
        // ----------------------------------------------------------------
        const pass = cameraConfigs.enableSingleForwardPass
            ? this._addForwardSingleRadiancePass(ppl, pplConfigs, cameraConfigs,
                id, camera, enableMSAA, width, height, mainLight,
                colorName, depthStencilName, depthStencilStoreOp)
            : this._addForwardMultipleRadiancePasses(ppl, cameraConfigs,
                id, camera, width, height, mainLight,
                colorName, depthStencilName, depthStencilStoreOp);

        // Planar Shadow
        if (cameraConfigs.enableMainLightPlanarShadowMap) {
            this._addPlanarShadowQueue(camera, mainLight, pass);
        }

        // ----------------------------------------------------------------
        // Forward Lighting (Blend)
        // ----------------------------------------------------------------
        // Add transparent queue

        const sceneFlags = SceneFlags.BLEND |
            (camera.geometryRenderer
                ? SceneFlags.GEOMETRY
                : SceneFlags.NONE);

        pass
            .addQueue(QueueHint.BLEND)
            .addScene(camera, sceneFlags, mainLight || undefined);

        return pass;
    }
    private _addForwardSingleRadiancePass(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,
        id: number,
        camera: renderer.scene.Camera,
        enableMSAA: boolean,
        width: number,
        height: number,
        mainLight: renderer.scene.DirectionalLight | null,
        colorName: string,
        depthStencilName: string,
        depthStencilStoreOp: gfx.StoreOp
    ): rendering.BasicRenderPassBuilder {
        assert(cameraConfigs.enableSingleForwardPass);
        // ----------------------------------------------------------------
        // Forward Lighting (Main Directional Light)
        // ----------------------------------------------------------------
        let pass: rendering.BasicRenderPassBuilder;
        if (enableMSAA) {
            const msaaRadianceName = `MsaaRadiance${id}`;
            const msaaDepthStencilName = `MsaaDepthStencil${id}`;
            const sampleCount = cameraConfigs.settings.msaa.sampleCount;

            const msPass = ppl.addMultisampleRenderPass(width, height, sampleCount, 0, 'default');
            msPass.name = 'MsaaForwardPass';

            // MSAA always discards depth stencil
            this._buildForwardMainLightPass(msPass, cameraConfigs, id, camera,
                msaaRadianceName, msaaDepthStencilName, StoreOp.DISCARD, mainLight);

            msPass.resolveRenderTarget(msaaRadianceName, colorName);

            pass = msPass;
        } else {
            pass = ppl.addRenderPass(width, height, 'default');
            pass.name = 'ForwardPass';

            this._buildForwardMainLightPass(pass, cameraConfigs, id, camera,
                colorName, depthStencilName, depthStencilStoreOp, mainLight);
        }
        assert(pass !== undefined);

        // Forward Lighting (Additive Lights)
        this.forwardLighting.addLightQueues(
            pass,
            camera,
            pplConfigs.mobileMaxSpotLightShadowMaps,
        );

        return pass;
    }
    private _addForwardMultipleRadiancePasses(
        ppl: rendering.BasicPipeline,
        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,
        id: number,
        camera: renderer.scene.Camera,
        width: number,
        height: number,
        mainLight: renderer.scene.DirectionalLight | null,
        colorName: string,
        depthStencilName: string,
        depthStencilStoreOp: gfx.StoreOp
    ): rendering.BasicRenderPassBuilder {
        assert(!cameraConfigs.enableSingleForwardPass);

        // Forward Lighting (Main Directional Light)
        let pass = ppl.addRenderPass(width, height, 'default');
        pass.name = 'ForwardPass';

        const firstStoreOp = this.forwardLighting.isMultipleLightPassesNeeded()
            ? StoreOp.STORE
            : depthStencilStoreOp;

        this._buildForwardMainLightPass(pass, cameraConfigs,
            id, camera, colorName, depthStencilName, firstStoreOp, mainLight);

        // Forward Lighting (Additive Lights)
        pass = this.forwardLighting
            .addLightPasses(colorName, depthStencilName, depthStencilStoreOp,
                id, width, height, camera, this._viewport, ppl, pass);

        return pass;
    }
    private _buildForwardMainLightPass(
        pass: rendering.BasicRenderPassBuilder,
        cameraConfigs: Readonly<CameraConfigs & ForwardPassConfigs>,
        id: number,
        camera: renderer.scene.Camera,
        colorName: string,
        depthStencilName: string,
        depthStencilStoreOp: gfx.StoreOp,
        mainLight: renderer.scene.DirectionalLight | null,
        scene: renderer.RenderScene | null = null,
    ): void {
        const QueueHint = rendering.QueueHint;
        const SceneFlags = rendering.SceneFlags;
        // set viewport
        pass.setViewport(this._viewport);

        const colorStoreOp = cameraConfigs.enableMSAA ? StoreOp.DISCARD : StoreOp.STORE;

        // bind output render target
        if (forwardNeedClearColor(camera)) {
            pass.addRenderTarget(colorName, LoadOp.CLEAR, colorStoreOp, this._clearColor);
        } else {
            pass.addRenderTarget(colorName, LoadOp.LOAD, colorStoreOp);
        }

        // bind depth stencil buffer
        if (DEBUG) {
            if (colorName === cameraConfigs.colorName &&
                depthStencilName !== cameraConfigs.depthStencilName) {
                warn('Default framebuffer cannot use custom depth stencil buffer');
            }
        }

        if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {
            pass.addDepthStencil(
                depthStencilName,
                LoadOp.CLEAR,
                depthStencilStoreOp,
                camera.clearDepth,
                camera.clearStencil,
                camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,
            );
        } else {
            pass.addDepthStencil(depthStencilName, LoadOp.LOAD, depthStencilStoreOp);
        }

        // Set shadow map if enabled
        if (cameraConfigs.enableMainLightShadowMap) {
            pass.addTexture(`ShadowMap${id}`, 'cc_shadowMap');
        }

        // TODO(zhouzhenglong): Separate OPAQUE and MASK queue

        // add opaque and mask queue
        pass.addQueue(QueueHint.NONE) // Currently we put OPAQUE and MASK into one queue, so QueueHint is NONE
            .addScene(camera,
                SceneFlags.OPAQUE | SceneFlags.MASK,
                mainLight || undefined,
                scene ? scene : undefined);
    }
    private _addPlanarShadowQueue(
        camera: renderer.scene.Camera,
        mainLight: renderer.scene.DirectionalLight | null,
        pass: rendering.BasicRenderPassBuilder,
    ) {
        const QueueHint = rendering.QueueHint;
        const SceneFlags = rendering.SceneFlags;
        pass.addQueue(QueueHint.BLEND, 'planar-shadow')
            .addScene(
                camera,
                SceneFlags.SHADOW_CASTER | SceneFlags.PLANAR_SHADOW | SceneFlags.BLEND,
                mainLight || undefined,
            );
    }
}
