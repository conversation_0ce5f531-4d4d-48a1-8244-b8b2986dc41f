import { gfx, Material, rendering, Vec2, Vec4 } from 'cc';
import { PipelineSettings, makePipelineSettings } from './builtin-pipeline-types';

const { Format } = gfx;

/**
 * 管线配置类
 */
export class PipelineConfigs {
    isWeb = false;
    isWebGL1 = false;
    isWebGPU = false;
    isMobile = false;
    isHDR = false;
    useFloatOutput = false;
    toneMappingType = 0; // 0: ACES, 1: None
    shadowEnabled = false;
    shadowMapFormat = Format.R32F;
    shadowMapSize = new Vec2(1, 1);
    usePlanarShadow = false;
    screenSpaceSignY = 1;
    supportDepthSample = false;
    mobileMaxSpotLightShadowMaps = 1;

    platform = new Vec4(0, 0, 0, 0);
}

/**
 * 扩展的管线设置接口
 */
export interface PipelineSettings2 extends PipelineSettings {
    _passes?: rendering.PipelinePassBuilder[];
}

const defaultSettings = makePipelineSettings();

/**
 * 相机配置类
 */
export class CameraConfigs {
    settings: PipelineSettings = defaultSettings;
    // Window
    isMainGameWindow = false;
    renderWindowId = 0;
    // Camera
    colorName = '';
    depthStencilName = '';
    // Pipeline
    enableFullPipeline = false;
    enableProfiler = false;
    remainingPasses = 0;
    // Shading Scale
    enableShadingScale = false;
    shadingScale = 1.0;
    nativeWidth = 1;
    nativeHeight = 1;
    width = 1; // Scaled width
    height = 1; // Scaled height
    // Radiance
    enableHDR = false;
    radianceFormat = gfx.Format.RGBA8;
    // Tone Mapping
    copyAndTonemapMaterial: Material | null = null;
    // Depth
    /** @en mutable */
    enableStoreSceneDepth = false;
}

/**
 * 管线上下文接口
 */
export interface PipelineContext {
    colorName: string;
    depthStencilName: string;
}

/**
 * Forward Pass 配置接口
 */
export interface ForwardPassConfigs {
    enableMainLightShadowMap: boolean;
    enableMainLightPlanarShadowMap: boolean;
    enablePlanarReflectionProbe: boolean;
    enableMSAA: boolean;
    enableSingleForwardPass: boolean;
}

/**
 * Bloom Pass 配置接口
 */
export interface BloomPassConfigs {
    enableBloom: boolean;
}

/**
 * Tone Mapping Pass 配置接口
 */
export interface ToneMappingPassConfigs {
    enableToneMapping: boolean;
    enableColorGrading: boolean;
}

/**
 * FXAA Pass 配置接口
 */
export interface FXAAPassConfigs {
    enableFXAA: boolean;
}

/**
 * FSR Pass 配置接口
 */
export interface FSRPassConfigs {
    enableFSR: boolean;
}
