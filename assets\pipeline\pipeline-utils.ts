import {
    assert, geometry, gfx, Material, pipeline, renderer, rendering, sys, Vec2, Vec4,
} from 'cc';

import { PipelineConfigs, CameraConfigs } from './pipeline-configs';

const { ClearFlagBit, Color, Format, FormatFeatureBit, LoadOp, StoreOp, Viewport } = gfx;
const { scene } = renderer;
const { CSMLevel } = scene;

const sClearColorTransparentBlack = new Color(0, 0, 0, 0);

/**
 * 判断前向渲染是否需要清除颜色
 */
export function forwardNeedClearColor(camera: renderer.scene.Camera): boolean {
    return !!(camera.clearFlag & (ClearFlagBit.COLOR | (ClearFlagBit.STENCIL << 1)));
}

/**
 * 获取CSM主光源视口
 */
export function getCsmMainLightViewport(
    light: renderer.scene.DirectionalLight,
    w: number,
    h: number,
    level: number,
    vp: gfx.Viewport,
    screenSpaceSignY: number,
): void {
    if (light.shadowFixedArea || light.csmLevel === CSMLevel.LEVEL_1) {
        vp.left = 0;
        vp.top = 0;
        vp.width = Math.trunc(w);
        vp.height = Math.trunc(h);
    } else {
        vp.left = Math.trunc(level % 2 * 0.5 * w);
        if (screenSpaceSignY > 0) {
            vp.top = Math.trunc((1 - Math.floor(level / 2)) * 0.5 * h);
        } else {
            vp.top = Math.trunc(Math.floor(level / 2) * 0.5 * h);
        }
        vp.width = Math.trunc(0.5 * w);
        vp.height = Math.trunc(0.5 * h);
    }
    vp.left = Math.max(0, vp.left);
    vp.top = Math.max(0, vp.top);
    vp.width = Math.max(1, vp.width);
    vp.height = Math.max(1, vp.height);
}

/**
 * 设置管线配置
 */
export function setupPipelineConfigs(
    ppl: rendering.BasicPipeline,
    configs: PipelineConfigs,
): void {
    const sampleFeature = FormatFeatureBit.SAMPLED_TEXTURE | FormatFeatureBit.LINEAR_FILTER;
    const device = ppl.device;
    // Platform
    configs.isWeb = !sys.isNative;
    configs.isWebGL1 = device.gfxAPI === gfx.API.WEBGL;
    configs.isWebGPU = device.gfxAPI === gfx.API.WEBGPU;
    configs.isMobile = sys.isMobile;

    // Rendering
    configs.isHDR = ppl.pipelineSceneData.isHDR; // Has tone mapping
    configs.useFloatOutput = ppl.getMacroBool('CC_USE_FLOAT_OUTPUT');
    configs.toneMappingType = ppl.pipelineSceneData.postSettings.toneMappingType;
    // Shadow
    const shadowInfo = ppl.pipelineSceneData.shadows;
    configs.shadowEnabled = shadowInfo.enabled;
    configs.shadowMapFormat = pipeline.supportsR32FloatTexture(ppl.device) ? Format.R32F : Format.RGBA8;
    configs.shadowMapSize.set(shadowInfo.size);
    configs.usePlanarShadow = shadowInfo.enabled && shadowInfo.type === renderer.scene.ShadowType.Planar;
    // Device
    configs.screenSpaceSignY = ppl.device.capabilities.screenSpaceSignY;
    configs.supportDepthSample = (ppl.device.getFormatFeatures(Format.DEPTH_STENCIL) & sampleFeature) === sampleFeature;
    // Constants
    const screenSpaceSignY = device.capabilities.screenSpaceSignY;
    configs.platform.x = configs.isMobile ? 1.0 : 0.0;
    configs.platform.w = (screenSpaceSignY * 0.5 + 0.5) << 1 | (device.capabilities.clipSpaceSignY * 0.5 + 0.5);
}

/**
 * 按配置顺序排序管线Pass构建器
 */
export function sortPipelinePassBuildersByConfigOrder(passBuilders: rendering.PipelinePassBuilder[]): void {
    passBuilders.sort((a, b) => {
        return a.getConfigOrder() - b.getConfigOrder();
    });
}

/**
 * 按渲染顺序排序管线Pass构建器
 */
export function sortPipelinePassBuildersByRenderOrder(passBuilders: rendering.PipelinePassBuilder[]): void {
    passBuilders.sort((a, b) => {
        return a.getRenderOrder() - b.getRenderOrder();
    });
}

/**
 * 添加复制到屏幕的Pass
 */
export function addCopyToScreenPass(
    ppl: rendering.BasicPipeline,
    pplConfigs: Readonly<PipelineConfigs>,
    cameraConfigs: CameraConfigs,
    input: string,
): rendering.BasicRenderPassBuilder {
    assert(!!cameraConfigs.copyAndTonemapMaterial);
    const pass = ppl.addRenderPass(
        cameraConfigs.nativeWidth,
        cameraConfigs.nativeHeight,
        'cc-tone-mapping');
    pass.addRenderTarget(
        cameraConfigs.colorName,
        LoadOp.CLEAR, StoreOp.STORE,
        sClearColorTransparentBlack);
    pass.addTexture(input, 'inputTexture');
    pass.setVec4('g_platform', pplConfigs.platform);
    pass.addQueue(rendering.QueueHint.OPAQUE)
        .addFullscreenQuad(cameraConfigs.copyAndTonemapMaterial, 1);
    return pass;
}

/**
 * 获取乒乓渲染目标名称
 */
export function getPingPongRenderTarget(prevName: string, prefix: string, id: number): string {
    if (prevName.startsWith(prefix)) {
        return `${prefix}${1 - Number(prevName.charAt(prefix.length))}_${id}`;
    } else {
        return `${prefix}0_${id}`;
    }
}
